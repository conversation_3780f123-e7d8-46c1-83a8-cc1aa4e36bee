import { UploadRequest } from './upload';
import { CompleteBody } from '../types';

export class CompleteRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly body: CompleteBody | null) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    if (this.body === null) {
      return this.error("Missing or incomplete body");
    }
    // Error handling in case the multipart upload does not exist anymore
    try {
      const object = await this.upload.complete(this.body.parts);
      return new Response(null, {
        headers: {
          etag: object.httpEtag
        }
      });
    } catch (error: any) {
      return this.error(error);
    }
  }
}
